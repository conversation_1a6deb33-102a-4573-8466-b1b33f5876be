{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nx", "prettier", "jest"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript", "plugin:prettier/recommended", "@react-native-community", "prettier"], "rules": {"react-hooks/exhaustive-deps": "off"}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript", "plugin:prettier/recommended", "prettier"], "rules": {}}]}