<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/>
    <uses-permission android:name="android.permission.BLUETOOTH" tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
                     android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>

  <!-- Add these WiFi-specific permissions -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" 
                 tools:node="remove"/>

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:theme="@style/AppTheme"
      tools:replace="android:allowBackup"
      android:allowBackup="false"
      android:usesCleartextTraffic="true"
      android:supportsRtl="true"
      android:networkSecurityConfig="@xml/network_security_config">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        <intent-filter>
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="fxblox" />
        </intent-filter>
      </activity>      
      <service android:name="com.asterinet.react.bgactions.RNBackgroundActionsTask"  android:exported="false" />
      <service 
        android:name="app.notifee.core.ForegroundService" 
        android:foregroundServiceType="dataSync"
        tools:replace="android:foregroundServiceType" />
    </application>
</manifest>
