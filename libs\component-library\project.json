{"name": "component-library", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/component-library/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/component-library"], "options": {"jestConfig": "libs/component-library/jest.config.ts"}}, "build": {"executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/component-library", "tsConfig": "libs/component-library/tsconfig.lib.json", "main": "libs/component-library/src/index.ts", "assets": ["libs/component-library/*.md", "libs/component-library/package.json"]}}}}