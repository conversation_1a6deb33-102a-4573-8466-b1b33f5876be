{"testRunner": {"args": {"$0": "jest", "config": "./jest.config.json"}, "jest": {"setupTimeout": 120000}}, "apps": {"ios.debug": {"type": "ios.app", "build": "cd ../file-manager/ios && xcodebuild -workspace FileManager.xcworkspace -scheme FileManager -configuration Debug -sdk iphonesimulator -destination 'platform=iOS Simulator,name=iPhone 13' -derivedDataPath ./build -quiet", "binaryPath": "../file-manager/ios/build/Build/Products/Debug-iphonesimulator/FileManager.app"}, "ios.release": {"type": "ios.app", "build": "cd ../file-manager/ios && xcodebuild -workspace FileManager.xcworkspace -scheme FileManager -configuration Release -sdk iphonesimulator -destination 'platform=iOS Simulator,name=iPhone 13' -derivedDataPath ./build -quiet", "binaryPath": "../file-manager/ios/build/Build/Products/Release-iphonesimulator/FileManager.app"}, "android.debug": {"type": "android.apk", "build": "cd ../file-manager/android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug", "binaryPath": "../file-manager/android/app/build/outputs/apk/debug/app-debug.apk"}, "android.release": {"type": "android.apk", "build": "cd ../file-manager/android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release", "binaryPath": "../file-manager/android/app/build/outputs/apk/release/app-release.apk"}}, "devices": {"simulator": {"type": "ios.simulator", "device": {"type": "iPhone 13"}}, "emulator": {"type": "android.emulator", "device": {"avdName": "Pixel_4a_API_30"}}}, "configurations": {"ios.sim.release": {"device": "simulator", "app": "ios.release"}, "ios.sim.debug": {"device": "simulator", "app": "ios.debug"}, "android.emu.release": {"device": "emulator", "app": "android.release"}, "android.emu.debug": {"device": "emulator", "app": "android.debug"}}}