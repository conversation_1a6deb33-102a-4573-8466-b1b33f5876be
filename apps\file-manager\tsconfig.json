{"extends": "../../tsconfig.base.json", "compilerOptions": {"allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true}, "files": ["../../node_modules/@nx/react-native/typings/svg.d.ts"], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}], "exclude": ["node_modules"]}