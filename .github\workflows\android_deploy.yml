name: FxBlox Android Deploy

on: 
  workflow_dispatch:
  release:
    types: [published]

jobs:
  deploy-android:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20' # Use the same Node.js version as your project requirements
    - name: Set up JDK 18
      uses: actions/setup-java@v3
      with:
        java-version: '18'
        distribution: 'temurin'
    - name: Enable Corepack
      run: |
        corepack enable
        corepack prepare yarn@3.6.4 --activate
    - name: Setup Android SDK
      uses: android-actions/setup-android@v3
      with:
        packages: 'cmdline-tools;latest'
    - name: Yarn Install
      run: yarn install
    - name: Ensure Symlink
      run: npm run ensure:symlink
    - name: Install CMake
      run: |
        echo "##[group]Install CMake"
        sdkmanager --install "cmake;3.10.2.4988404"
        yes | sdkmanager --licenses
        echo "##[endgroup]"
    - name: Install Android Build Tools
      run: sdkmanager "build-tools;29.0.3"
    - name: List Build Tools Directory
      run: ls -la /usr/local/lib/android/sdk/build-tools/29.0.3
    - name: Add Build Tools to PATH
      run: echo "/usr/local/lib/android/sdk/build-tools/29.0.3" >> $GITHUB_PATH
    - name: Get NPM Package Version
      run: echo "NPM_PACKAGE_VERSION=$(node -p "require('${{ github.workspace }}/apps/box/package.json').version")" >> $GITHUB_ENV
    - name: Show GITHUB_ENV
      run: echo $GITHUB_ENV
    - name: Decode Keystore
      run: echo "${{ secrets.SIGNING_KEY_BASE64 }}" | base64 -d > ${{ github.workspace }}/apps/box/android/app/signingKey.jks
    - name: Build Android Release
      run: |
        cd ${{ github.workspace }}/apps/box/android
        chmod +x gradlew
        ./gradlew bundleRelease -Pandroid.injected.signing.store.file=$GITHUB_WORKSPACE/apps/box/android/app/signingKey.jks -Pandroid.injected.signing.store.password=$KEY_STORE_PASSWORD -Pandroid.injected.signing.key.alias=$KEY_ALIAS -Pandroid.injected.signing.key.password=$KEY_PASSWORD
      env:
        KEY_STORE_PASSWORD: ${{ secrets.KEY_STORE_PASSWORD }}
        KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
        KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
    
    - name: List Directory
      run: ls -la ${{ github.workspace }}/apps/box/android/app/build/outputs/bundle/release
      
    - name: Get Release Info
      id: get-release-info
      uses: actions/github-script@v5
      with:
        script: |
            let uploadUrl = 0;
            try {
              const release = await github.rest.repos.getLatestRelease({
                owner: context.repo.owner,
                repo: context.repo.repo,
              });
              uploadUrl = release.data.id;
            } catch (error) {
              console.log('Error fetching latest release: ', error.message);
            }
            return uploadUrl;
      
    - name: Print release upload URL
      run: echo "Upload URL is ${{ steps.get-release-info.outputs.result }}"
      
    - name: Upload Update File Parts to Release
      uses: actions/github-script@v5
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        script: |
            const fs = require('fs');
            const path = require('path');
            const directory = '${{ github.workspace }}/apps/box/android/app/build/outputs/bundle/release';
            const files = fs.readdirSync(directory);
            for (const file of files) {
              if (file.startsWith('app')) {
                const filePath = path.join(directory, file);
                console.log(`Uploading ${file}...`);
                await github.rest.repos.uploadReleaseAsset({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  release_id: ${{ steps.get-release-info.outputs.result }},
                  name: `FxBlox_${file}`,
                  data: fs.readFileSync(filePath)
                });
              }
            }
      