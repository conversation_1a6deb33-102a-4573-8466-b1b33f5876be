{"name": "functionland", "version": "0.0.0", "license": "MIT", "scripts": {"ios": "nx run-ios", "android": "nx run-android", "start": "nx serve", "build": "nx build", "test": "nx run-many --all --skip-nx-cache --target=test", "lint": "nx run-many --all --skip-nx-cache --target=lint", "ensure:symlink": "nx ensure-symlink box && nx ensure-symlink file-manager", "check-app-types": "tsc --noEmit --project apps/box/tsconfig.app.json && tsc --noEmit --project apps/file-manager/tsconfig.app.json", "check-lib-types": "tsc --noEmit --project libs/component-library/tsconfig.lib.json", "check": "yarn lint && yarn test && yarn check-app-types && yarn check-lib-types", "check-ci": "yarn run check"}, "private": true, "dependencies": {"@babel/core": "7.28.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.0.0-0", "@babel/plugin-proposal-optional-chaining": "^7.0.0-0", "@babel/plugin-proposal-private-methods": "*", "@babel/plugin-transform-arrow-functions": "7.27.1", "@babel/plugin-transform-shorthand-properties": "7.27.1", "@babel/plugin-transform-template-literals": "7.27.1", "@babel/preset-env": "7.28.0", "@ethersproject/shims": "5.8.0", "@functionland/fula-sec": "2.0.0", "@functionland/react-native-fula": "1.55.15", "@gorhom/bottom-sheet": "5.1.6", "@metamask/sdk": "0.33.0", "@metamask/sdk-react": "0.33.0", "@notifee/react-native": "9.1.8", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-clipboard/clipboard": "1.16.3", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "5.0.0", "@react-native-firebase/app": "22.4.0", "@react-native-firebase/crashlytics": "22.4.0", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "7.4.2", "@react-navigation/material-top-tabs": "7.3.2", "@react-navigation/native": "7.1.14", "@react-navigation/native-stack": "7.3.21", "@react-navigation/stack": "7.4.2", "@react-spring/three": "10.0.1", "@react-three/fiber": "9.2.0", "@rneui/base": "^4.0.0-rc.8", "@rneui/themed": "^4.0.0-rc.8", "@shopify/react-native-skia": "2.1.1", "@shopify/restyle": "2.4.5", "@visx/scale": "3.12.0", "@walletconnect/encoding": "1.0.2", "@walletconnect/react-native-compat": "2.21.5", "axios": "1.11.0", "big-integer": "1.6.52", "eciesjs": "0.4.15", "ethers": "6.15.0", "expo": "53.0.20", "expo-asset": "11.1.7", "expo-font": "13.3.2", "expo-gl": "15.1.7", "expo-three": "8.0.0", "i18next": "25.3.2", "i18next-resources-to-backend": "1.2.1", "moment": "2.30.1", "node-libs-react-native": "1.2.1", "react": "19.1.0", "react-content-loader": "7.1.1", "react-dom": "19.1.0", "react-i18next": "15.6.1", "react-native": "0.80.1", "react-native-background-timer": "2.4.1", "react-native-ble-manager": "12.1.5", "react-native-device-info": "14.0.4", "react-native-elements": "3.4.3", "react-native-gesture-handler": "2.27.2", "react-native-get-random-values": "1.11.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-keychain": "10.0.0", "react-native-localize": "3.5.1", "react-native-modal": "13.0.1", "react-native-pager-view": "6.8.1", "react-native-paper": "5.14.5", "react-native-permissions": "5.4.2", "react-native-qrcode-svg": "6.3.15", "react-native-randombytes": "^3.6.1", "react-native-reanimated": "4.0.0", "react-native-reanimated-carousel": "4.0.2", "react-native-redash": "18.1.3", "react-native-safe-area-context": "5.5.2", "react-native-screens": "4.13.1", "react-native-svg": "15.12.0", "react-native-svg-transformer": "1.5.1", "react-native-syntax-highlighter": "2.1.0", "react-native-tab-view": "4.1.2", "react-native-url-polyfill": "2.0.0", "react-native-vector-icons": "10.2.0", "react-native-webview": "13.15.0", "react-native-wheel-color-picker": "1.3.1", "react-native-wifi-reborn": "4.13.6", "react-native-zeroconf": "0.13.8", "text-encoding-polyfill": "0.6.7", "three": "0.178.0", "tslib": "2.8.1", "viem": "2.33.0", "wagmi": "2.16.0", "whatwg-fetch": "3.6.20", "zustand": "5.0.6"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-class-static-block": "7.27.1", "@nrwl/js": "19.8.14", "@nrwl/workspace": "19.8.14", "@nx/detox": "21.3.3", "@nx/eslint": "21.3.3", "@nx/eslint-plugin": "21.3.3", "@nx/jest": "21.3.3", "@nx/react-native": "21.3.3", "@nx/web": "21.3.3", "@react-native-community/cli": "19.1.0", "@react-native-community/cli-platform-android": "19.1.0", "@react-native-community/cli-platform-ios": "19.1.0", "@react-native-community/eslint-config": "3.2.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@testing-library/jest-dom": "6.6.3", "@testing-library/jest-native": "5.4.3", "@testing-library/react-native": "13.2.0", "@types/axios": "0.9.36", "@types/jest": "30.0.0", "@types/node": "24.1.0", "@types/react": "19.1.8", "@types/react-native-background-timer": "2.0.2", "@types/three": "0.178.1", "@typescript-eslint/eslint-plugin": "8.38.0", "@typescript-eslint/parser": "8.38.0", "babel-jest": "30.0.5", "detox": "20.40.2", "eslint": "9.31.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "2.32.0", "eslint-plugin-jest": "29.0.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.5.3", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "jest": "30.0.5", "jest-circus": "30.0.5", "jest-environment-jsdom": "30.0.5", "jest-react-native": "18.0.0", "metro": "0.83.0", "metro-babel-register": "0.83.0", "metro-react-native-babel-preset": "0.77.0", "metro-resolver": "0.83.0", "nx": "21.3.3", "prettier": "3.6.2", "react-native-config": "1.5.5", "react-test-renderer": "19.1.0", "ts-jest": "29.4.0", "ts-node": "10.9.2", "typescript": "5.8.3"}, "packageManager": "yarn@3.6.4+sha512.e70835d4d6d62c07be76b3c1529cb640c7443f0fe434ef4b6478a5a399218cbaf1511b396b3c56eb03bc86424cff2320f6167ad2fde273aa0df6e60b7754029f"}