{"welcome": {"title": "你好，Functionlander！", "appTitle": "Blox 应用", "disclaimer": "使用本产品即表示您同意 fx.land/terms 上的条款和条件，并且不会因数据丢失而追究 Functionland 的责任。", "termsButton": "条款和条件", "setupButton": "同意并设置我的 Blox"}, "connectToWallet": {"title": "连接到钱包", "description": "应用程序需要通知权限才能连接您的钱包并执行数据同步。在下一个提示中点击允许。", "allowButton": "允许通知"}, "linkPassword": {"title": "设置身份", "yourIdentity": "您的身份", "password": "密码", "signature": "签名", "warning": "确保保护好此密码和您使用的链，它是从新设备解密数据的关键", "passwordRisk": "我了解丢失密码的风险", "metamaskOpen": "点击签名前我已经打开了 Metamask 应用", "connectingWallet": "正在连接钱包...", "walletConnectionInProgress": "钱包连接正在进行中，点击返回应用", "error": "错误", "unableToSignWallet": "无法签署钱包地址！", "signWithMetamask": "使用 MetaMask 签名", "cancel": "取消", "signManually": "手动签名", "submit": "提交", "connectToBlox": "连接到 Blox", "reconnectExisting": "重新连接到现有 blox", "bluetoothCommands": "蓝牙命令", "skipManualSetup": "跳到手动设置"}, "connectToBlox": {"title": "连接到 Blox", "checking": "正在检查连接...", "connected": "已连接", "failed": "无法连接到热点", "notConnected": "未连接", "bleConnecting": "正在搜索 Blox 设备...", "bleConnected": "已通过蓝牙连接到 Blox", "bleFailed": "无法通过蓝牙连接，尝试 WiFi...", "hotspotInstructions": "- 请打开 Blox，手动将手机连接到 Blox 的热点，并关闭移动数据。", "formatInstructions": "- 确保您已连接内部或外部存储，格式为 ext4 或 vFat 。", "waitForBlueLight": "首次启动后请等待 10 分钟，直到 Blox 闪烁 淡蓝色", "connectedMessage": "现在您已连接到 Blox。请稍候...", "back": "返回", "continue": "继续"}, "connectToWifi": {"title": "连接到 Wi-Fi", "manualEntry": "手动输入 Wifi 名称", "showNetworks": "显示网络名称", "enterWifiName": "输入 Wifi 名称", "enterPasswordFor": "输入密码为", "searching": "正在搜索 Wi-Fi 网络", "selectNetwork": "选择 Wi-Fi 网络", "back": "返回", "next": "下一步"}, "setBloxAuthorizer": {"title": "设置 Blox 所有者", "description": "将 Blox 应用程序对等 ID 添加为 Blox 上的所有者", "networkError": "在某些情况下，您需要关闭移动数据，请确保手机连接到 Blox 的热点，并且移动数据/VPN 已关闭，然后按返回并再次尝试", "updateNeeded": "更新等待手动重启才能应用。您应该拔掉并重新插入 blox 以重新启动它，然后再次尝试。", "backendUpdate": "您应该更新 blox 后端，请按 跳过 按钮并将其连接到您的 Wifi 网络。", "storageNeeded": "要成功进行，您需要将外部存储连接到 Blox！", "appPeerId": "Blox 应用程序对等 ID", "generating": "正在生成 app peerId...", "bloxPeerId": "您的 Blox 对等 ID", "enterBloxPeerId": "输入您的 Blox 对等 ID", "setBloxName": "设置 Blox 名称", "hardDisk": "硬盘", "bloxSetUp": "Blox 设置", "formatDisk": "格式化磁盘", "skip": "跳过", "skipAuthorization": "跳过授权", "skipDescription": "跳过仅在支持团队指示您这样做时才适用。请输入给您的代码：", "cancel": "取消", "invalidCode": "无效代码", "invalidCodeMessage": "您输入的代码不正确。如果您需要帮助，请联系支持团队。", "confirm": "确认", "back": "返回", "setAuthorizer": "设置授权者", "next": "下一步", "bloxUnitPrefix": "Blox 单元", "noBleDevicesConnected": "未连接 BLE 设备", "unableToGetProperties": "无法获取 blox 属性！", "bloxPeerIdInvalid": "Blox peerId 无效！"}, "setupComplete": {"completing": "正在完成设置", "connectPhone": "立即将手机连接到互联网（wifi）以继续...", "reachingBlox": "正在连接 Blox #{{number}}...", "greenLed": "您的 blox LED 是 绿色 但您看到此消息？", "internetReminder": "确保您的手机已连接到互联网，然后重试", "lightBlueLed": "您的 blox 闪烁 淡蓝色 ？您可能输入了错误的 wifi 密码", "notReachable": "您的 Blox 无法访问，似乎未连接到互联网！请关闭 blox，然后打开它并确保处于热点模式，然后尝试将 blox 重新连接到 Wi-Fi", "updating": "您的 blox 正在更新。请等待一小时完成更新。", "disconnectHotspot": "同时，您可以断开手机与 FxBlox 热点的连接。", "homeScreen": "主屏幕", "congratulations": "恭喜", "setupComplete": "设置完成", "home": "首页", "checkInternet": "检查互联网连接", "wrongPassword": "输入了错误的密码？返回", "checkConnection": "再次检查连接", "cyanFlashing": "如果 Blox 闪烁 青色 ，这可能意味着您输入了错误的 wifi 密码。再次连接到 FxBlox Wifi 并重试。", "back": "返回", "reconnectWifi": "将 Blox 重新连接到 Wi-Fi", "notConnectedToHotspot": "您似乎不再连接到热点，请检查 FxBlox 热点是否仍然可用，如果不可用，blox 已连接到互联网，您可以前往首页", "unableToInitialize": "无法初始化 fula 网络！错误：{{error}} 对于 fulaIsReady={{fulaIsReady}}"}, "connectToExistingBlox": {"title": "您网络中的 Bloxs", "selectBloxs": "选择您要添加的 bloxs", "bloxUnitPrefix": "Blox 单元", "ip": "IP", "peerId": "对等 ID", "hardwareId": "硬件 ID", "authorized": "已授权", "notAuthorized": "未授权", "checking": "检查中...", "alreadyExist": "已存在", "addSelectedBloxs": "添加选定的 blox", "generateAppPeerIdError": "连接到现有Blox屏幕:生成应用程序对等ID: "}, "checkConnection": {"verifyingConnectionWith": "正在验证与 {{ssid}} 的连接", "successfullyConnected": "已成功连接到 {{ssid}}。", "verifyingConnection": "正在验证连接...", "couldntConnect": "无法连接 {{ssid}}。", "couldntConnectTryAgain": "无法连接 {{ssid}}。请重试。", "connectingWith": "正在连接 {{ssid}}...", "allDone": "全部完成\n您的设备已成功连接！"}}