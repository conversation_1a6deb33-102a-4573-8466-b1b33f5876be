{"testRunner": {"args": {"$0": "jest", "config": "./jest.config.json"}, "jest": {"setupTimeout": 120000}}, "apps": {"ios.debug": {"type": "ios.app", "build": "cd ../box/ios && xcodebuild -workspace Box.xcworkspace -scheme Box -configuration Debug -sdk iphonesimulator -destination 'platform=iOS Simulator,name=iPhone 13' -derivedDataPath ./build -quiet", "binaryPath": "../box/ios/build/Build/Products/Debug-iphonesimulator/Box.app"}, "ios.release": {"type": "ios.app", "build": "cd ../box/ios && xcodebuild -workspace Box.xcworkspace -scheme Box -configuration Release -sdk iphonesimulator -destination 'platform=iOS Simulator,name=iPhone 13' -derivedDataPath ./build -quiet", "binaryPath": "../box/ios/build/Build/Products/Release-iphonesimulator/Box.app"}, "android.debug": {"type": "android.apk", "build": "cd ../box/android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug", "binaryPath": "../box/android/app/build/outputs/apk/debug/app-debug.apk"}, "android.release": {"type": "android.apk", "build": "cd ../box/android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release", "binaryPath": "../box/android/app/build/outputs/apk/release/app-release.apk"}}, "devices": {"simulator": {"type": "ios.simulator", "device": {"type": "iPhone 13"}}, "emulator": {"type": "android.emulator", "device": {"avdName": "Pixel_4a_API_30"}}}, "configurations": {"ios.sim.release": {"device": "simulator", "app": "ios.release"}, "ios.sim.debug": {"device": "simulator", "app": "ios.debug"}, "android.emu.release": {"device": "emulator", "app": "android.release"}, "android.emu.debug": {"device": "emulator", "app": "android.debug"}}}