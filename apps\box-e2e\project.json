{"name": "box-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/box-e2e/src", "projectType": "application", "targets": {"build-ios": {"executor": "@nx/detox:build", "options": {"detoxConfiguration": "ios.sim.debug"}, "configurations": {"production": {"detoxConfiguration": "ios.sim.release"}}}, "test-ios": {"executor": "@nx/detox:test", "options": {"detoxConfiguration": "ios.sim.debug", "buildTarget": "box-e2e:build-ios"}, "configurations": {"production": {"detoxConfiguration": "ios.sim.release", "buildTarget": "box-e2e:build-ios:prod"}}}, "build-android": {"executor": "@nx/detox:build", "options": {"detoxConfiguration": "android.emu.debug"}, "configurations": {"production": {"detoxConfiguration": "android.emu.release"}}}, "test-android": {"executor": "@nx/detox:test", "options": {"detoxConfiguration": "android.emu.debug", "buildTarget": "box-e2e:build-android"}, "configurations": {"production": {"detoxConfiguration": "android.emu.release", "buildTarget": "box-e2e:build-android:prod"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}}, "tags": [], "implicitDependencies": ["box"]}