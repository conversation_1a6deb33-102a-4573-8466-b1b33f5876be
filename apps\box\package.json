{"name": "box", "version": "1.6.35", "private": true, "dependencies": {"@functionland/fula-sec": "2.0.0", "@functionland/react-native-fula": "1.55.15", "@gorhom/bottom-sheet": "5.1.6", "@metamask/sdk-react": "0.33.0", "@notifee/react-native": "9.1.8", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-clipboard/clipboard": "1.16.3", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "5.0.0", "@react-native-firebase/app": "22.4.0", "@react-native-firebase/crashlytics": "22.4.0", "@react-native-picker/picker": "2.11.1", "@react-native/metro-config": "0.80.1", "@react-navigation/bottom-tabs": "7.4.2", "@react-navigation/material-top-tabs": "7.3.2", "@react-navigation/native": "7.1.14", "@react-navigation/native-stack": "7.3.21", "@react-navigation/stack": "7.4.2", "@react-spring/three": "10.0.1", "@react-three/fiber": "9.2.0", "@shopify/react-native-skia": "2.1.1", "@shopify/restyle": "2.4.5", "@testing-library/jest-native": "*", "@testing-library/react-native": "13.2.0", "@visx/scale": "3.12.0", "@walletconnect/encoding": "1.0.2", "@walletconnect/react-native-compat": "2.21.5", "@web3modal/wagmi-react-native": "*", "axios": "1.10.0", "big-integer": "1.6.52", "expo": "53.0.20", "expo-gl": "15.1.7", "expo-three": "8.0.0", "metro-config": "0.83.0", "moment": "2.30.1", "node-libs-react-native": "1.2.1", "react": "19.1.0", "react-content-loader": "7.1.1", "react-native": "0.80.1", "react-native-background-timer": "2.4.1", "react-native-ble-manager": "12.1.5", "react-native-config": "1.5.5", "react-native-device-info": "14.0.4", "react-native-elements": "3.4.3", "react-native-gesture-handler": "2.27.2", "react-native-get-random-values": "1.11.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-keychain": "10.0.0", "react-native-localize": "3.5.1", "react-native-modal": "13.0.1", "react-native-pager-view": "6.8.1", "react-native-paper": "5.14.5", "react-native-randombytes": "*", "react-native-reanimated": "3.19.0", "react-native-reanimated-carousel": "4.0.2", "react-native-redash": "18.1.3", "react-native-safe-area-context": "5.5.2", "react-native-screens": "4.13.1", "react-native-svg": "15.12.0", "react-native-svg-transformer": "1.5.1", "react-native-tab-view": "4.1.2", "react-native-url-polyfill": "2.0.0", "react-native-webview": "13.15.0", "react-native-wheel-color-picker": "1.3.1", "react-native-wifi-reborn": "4.13.6", "react-native-zeroconf": "0.13.8", "text-encoding-polyfill": "0.6.7", "three": "0.178.0", "viem": "2.33.0", "wagmi": "2.16.0", "zustand": "5.0.6", "@types/react-native-background-timer": "2.0.2", "ethers": "6.15.0", "whatwg-fetch": "3.6.20", "react-native-permissions": "5.4.2", "base-64": "1.0.0", "react-native-syntax-highlighter": "2.1.0", "react-syntax-highlighter": "15.6.1", "i18next": "25.3.2", "react-i18next": "15.6.1", "i18next-resources-to-backend": "1.2.1"}, "devDependencies": {"@types/react-native-background-timer": "2.0.2"}}