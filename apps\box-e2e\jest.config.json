{"preset": "../../jest.preset", "testEnvironment": "./environment", "testRunner": "jest-circus/runner", "testTimeout": 120000, "reporters": ["detox/runners/jest/streamlineReporter"], "setupFilesAfterEnv": ["<rootDir>/test-setup.ts"], "rootDir": ".", "testMatch": ["<rootDir>/src/**/*.test.ts?(x)", "<rootDir>/src/**/*.spec.ts?(x)"], "reporter": ["detox/runners/jest/reporter"], "globalSetup": "detox/runners/jest/globalSetup", "globalTeardown": "detox/runners/jest/globalTeardown", "verbose": true}