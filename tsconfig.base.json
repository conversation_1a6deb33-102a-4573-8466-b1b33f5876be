{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "strict": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@functionland/component-library": ["libs/component-library/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}