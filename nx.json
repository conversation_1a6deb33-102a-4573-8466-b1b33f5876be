{"$schema": "./node_modules/nx/schemas/nx-schema.json", "affected": {"defaultBase": "main"}, "defaultProject": "box", "targetDefaults": {"@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": ["{workspaceRoot}/babel.config.json"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s"]}}