{"migrations": [{"cli": "nx", "version": "17.3.0-beta.6", "description": "Updates the nx wrapper.", "implementation": "./src/migrations/update-17-3-0/update-nxw", "package": "nx", "name": "17.3.0-update-nx-wrapper"}, {"version": "17.2.0-beta.0", "description": "Simplify eslintFilePatterns", "implementation": "./src/migrations/update-17-2-0/simplify-eslint-patterns", "package": "@nx/eslint", "name": "simplify-eslint-patterns"}, {"version": "17.2.9", "description": "Move executor options to target defaults", "implementation": "./src/migrations/update-17-2-9/move-options-to-target-defaults", "package": "@nx/eslint", "name": "move-options-to-target-defaults"}]}